# 🚀 Sora批量出图工具 V3.3

> **一键批量生成高质量AI图片的神器工具**  
> 支持多平台API，智能参考图匹配，高并发处理，让AI出图变得简单高效！

## ✨ 核心功能特色

### 🎯 批量智能生成
- **CSV批量导入** - 一次导入几百个提示词，自动批量生成
- **智能生成模式** - 只生成新增的提示词，避免重复工作
- **高速并发处理** - 最高支持1000个并发，大幅提升生成速度
- **失败自动重试** - 网络问题自动重试，确保成功率

### 🖼️ 智能参考图系统
- **分类管理** - 按家庭、角色、场景等分类管理参考图
- **自动匹配** - 在提示词中提到图片名称，自动添加对应参考图
- **本地存储** - 参考图保存在本地，加载速度快
- **全局唯一命名** - 确保图片名称不重复，避免混淆

### 🎨 风格库系统
- **预设风格** - 内置超写实、皮克斯、动漫等多种风格
- **自定义风格** - 创建和保存自己的专属风格模板
- **一键应用** - 选择风格后自动应用到所有提示词
- **风格导入导出** - 分享和备份风格设置

### 🔑 多平台密钥管理
- **多平台支持** - 支持云雾AI、API易、apicore三大平台
- **密钥库管理** - 安全存储多个API密钥，随时切换
- **使用记录** - 自动记录密钥使用时间和平台信息

### 📁 智能文件管理
- **防覆盖保护** - 同名文件自动添加后缀（如：1.png → 1-2.png）
- **实时缩略图** - 生成完成立即显示缩略图预览
- **状态跟踪** - 实时显示等待、生成中、下载中、成功等状态
- **批量操作** - 支持选中重新生成、全部重新生成等操作

## 🚀 快速开始指南

### 第一步：基础配置

1. **双击运行** `sora批量出图启动.bat` 启动工具
2. **点击 "⚙️ 设置中心"** 进入配置界面
3. **配置API密钥**：
   - 在"密钥库"标签页点击"➕ 新建密钥"
   - 输入容易识别的名称（如：我的云雾密钥）
   - 选择对应的API平台
   - 输入完整的API密钥
   - 点击保存
4. **选择密钥**：在"基础配置"标签页选择刚创建的密钥
5. **设置保存路径**：选择图片保存的文件夹
6. **调整参数**：
   - 并发线程数：建议根据API限制设置（1-50）
   - 失败重试次数：建议2-3次
   - 图片比例：选择3:2或2:3

### 第二步：准备参考图（可选）

如果需要使用参考图：

1. **创建分类**：
   - 在设置中心的"参考图库"标签页
   - 点击"➕ 新建分类"，如"角色图片"
2. **添加图片**：
   - 选择分类后点击"➕ 添加图片"
   - 选择本地图片文件
   - 输入图片名称（如：小明、小红）
   - 图片会自动复制到项目目录

### 第三步：设置风格（可选）

1. **选择预设风格**：在"风格库"标签页选择喜欢的风格
2. **或创建自定义风格**：
   - 点击"➕ 新建"创建新风格
   - 输入风格描述内容
   - 保存并应用

### 第四步：批量生成图片

#### 方法一：CSV文件批量导入

1. **准备CSV文件**，包含以下列：
   ```csv
   分镜编号,分镜提示词
   001,小明在公园里踢足球
   002,小红在图书馆看书
   003,一家人在餐厅吃饭
   ```

2. **导入文件**：
   - 点击主界面的"📁 导入CSV文件"
   - 选择准备好的CSV文件
   - 确认提示词正确加载

3. **开始生成**：
   - 确认配置无误后点击"🚀 智能生成(仅新增)"
   - 工具会自动处理所有提示词

#### 方法二：手动添加提示词

1. **添加提示词**：
   - 点击"➕ 添加提示词"
   - 输入提示词内容
   - 重复添加多个提示词

2. **开始生成**：点击"🚀 智能生成(仅新增)"

## 🎯 高级使用技巧

### 智能参考图匹配

在提示词中提到图片名称，工具会自动添加对应的参考图：

```
提示词：小明在公园里踢足球
结果：自动加载名为"小明"的参考图

提示词：小红和小明一起看书  
结果：自动加载"小红"和"小明"两张参考图
```

### 批量重新生成

- **重新生成选中**：选中几个提示词，点击"🔄 重新生成选中"
- **重新生成全部**：点击"🔄 重新生成全部"重新生成所有图片

### 风格应用

在主界面选择风格后，所有提示词都会自动应用该风格：
- 选择"超写实风格" → 所有图片都将是写实风格
- 选择"皮克斯风格" → 所有图片都将是动画风格

### 编辑功能

- **编辑提示词**：双击提示词内容可以修改
- **编辑编号**：单击编号可以修改
- **删除操作**：选中后点击"🗑️ 删除选中"

## 📊 状态说明

| 状态 | 说明 |
|------|------|
| 等待中 | 提示词已添加，等待生成 |
| 生成中 | 正在调用API生成图片 |
| 下载中 | 图片生成完成，正在下载 |
| ✅ 成功 | 图片生成并下载完成 |
| ❌ 失败 | 生成失败，查看错误信息 |

## 💡 使用提示

### 提升成功率
- **API密钥确保有效** 且有足够额度
- **网络连接稳定**，避免频繁断网
- **并发数不要过高**，避免API限流
- **提示词描述清晰**，避免违规内容

### 提升效率
- **使用CSV批量导入** 处理大量提示词
- **合理设置并发数**（建议10-50个）
- **提前准备参考图** 和风格设置
- **定期保存配置** 避免重复设置

### 文件管理
- **定期清理图片文件夹** 避免文件过多
- **备份重要的风格设置** 使用导出功能
- **重要图片及时转移** 到其他位置保存

## ❓ 常见问题

**Q：为什么显示"API密钥不能为空"？**  
A：请在设置中心的密钥库中添加有效的API密钥并选择。

**Q：为什么一直显示"生成中"？**  
A：可能是网络问题或API平台繁忙，请检查网络连接和API额度。

**Q：参考图为什么没有自动加载？**  
A：确保提示词中包含完整的图片名称，且图片名称在系统中是唯一的。

**Q：如何避免图片被覆盖？**  
A：工具已自动处理，同名图片会添加后缀（如1-2.png）。

**Q：可以同时使用多个API平台吗？**  
A：可以在密钥库中添加多个平台的密钥，但同一时间只能使用一个。

---

**享受AI批量出图的乐趣吧！🎉** 