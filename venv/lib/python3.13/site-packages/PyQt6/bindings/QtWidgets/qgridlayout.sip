// qgridlayout.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QGridLayout : public QLayout
{
%TypeHeaderCode
#include <qgridlayout.h>
%End

public:
    explicit QGridLayout(QWidget *parent /TransferThis/ = 0);
    virtual ~QGridLayout();
    virtual QSize sizeHint() const;
    virtual QSize minimumSize() const;
    virtual QSize maximumSize() const;
    void setRowStretch(int row, int stretch);
    void setColumnStretch(int column, int stretch);
    int rowStretch(int row) const;
    int columnStretch(int column) const;
    void setRowMinimumHeight(int row, int minSize);
    void setColumnMinimumWidth(int column, int minSize);
    int rowMinimumHeight(int row) const;
    int columnMinimumWidth(int column) const;
    int columnCount() const;
    int rowCount() const;
    QRect cellRect(int row, int column) const;
    virtual bool hasHeightForWidth() const;
    virtual int heightForWidth(int) const;
    virtual int minimumHeightForWidth(int) const;
    virtual Qt::Orientations expandingDirections() const;
    virtual void invalidate();
    void addWidget(QWidget *w /GetWrapper/);
%MethodCode
        Py_BEGIN_ALLOW_THREADS
        sipCpp->addWidget(a0);
        Py_END_ALLOW_THREADS
        
        // The layout's parent widget (if there is one) will now have ownership.
        QWidget *parent = sipCpp->parentWidget();
        
        if (parent)
        {
            PyObject *py_parent = sipGetPyObject(parent, sipType_QWidget);
        
            if (py_parent)
                sipTransferTo(a0Wrapper, py_parent);
        }
        else
        {
            // For now give the Python ownership to the layout.  This maintains
            // compatibility with previous versions and allows addWidget(QWidget()).
            sipTransferTo(a0Wrapper, sipSelf);
        }
%End

    void addWidget(QWidget * /GetWrapper/, int row, int column, Qt::Alignment alignment = Qt::Alignment());
%MethodCode
        Py_BEGIN_ALLOW_THREADS
        sipCpp->addWidget(a0, a1, a2, *a3);
        Py_END_ALLOW_THREADS
        
        // The layout's parent widget (if there is one) will now have ownership.
        QWidget *parent = sipCpp->parentWidget();
        
        if (parent)
        {
            PyObject *py_parent = sipGetPyObject(parent, sipType_QWidget);
        
            if (py_parent)
                sipTransferTo(a0Wrapper, py_parent);
        }
        else
        {
            // For now give the Python ownership to the layout.  This maintains
            // compatibility with previous versions and allows addWidget(QWidget()).
            sipTransferTo(a0Wrapper, sipSelf);
        }
%End

    void addWidget(QWidget * /GetWrapper/, int row, int column, int rowSpan, int columnSpan, Qt::Alignment alignment = Qt::Alignment());
%MethodCode
        Py_BEGIN_ALLOW_THREADS
        sipCpp->addWidget(a0, a1, a2, a3, a4, *a5);
        Py_END_ALLOW_THREADS
        
        // The layout's parent widget (if there is one) will now have ownership.
        QWidget *parent = sipCpp->parentWidget();
        
        if (parent)
        {
            PyObject *py_parent = sipGetPyObject(parent, sipType_QWidget);
        
            if (py_parent)
                sipTransferTo(a0Wrapper, py_parent);
        }
        else
        {
            // For now give the Python ownership to the layout.  This maintains
            // compatibility with previous versions and allows addWidget(QWidget()).
            sipTransferTo(a0Wrapper, sipSelf);
        }
%End

    void addLayout(QLayout * /Transfer/, int row, int column, Qt::Alignment alignment = Qt::Alignment());
    void addLayout(QLayout * /Transfer/, int row, int column, int rowSpan, int columnSpan, Qt::Alignment alignment = Qt::Alignment());
    void setOriginCorner(Qt::Corner);
    Qt::Corner originCorner() const;
    virtual QLayoutItem *itemAt(int) const;
    virtual QLayoutItem *takeAt(int) /TransferBack/;
    virtual int count() const;
    virtual void setGeometry(const QRect &);
    void addItem(QLayoutItem *item /Transfer/, int row, int column, int rowSpan = 1, int columnSpan = 1, Qt::Alignment alignment = Qt::Alignment());
    void setDefaultPositioning(int n, Qt::Orientation orient);
    void getItemPosition(int idx, int *row, int *column, int *rowSpan, int *columnSpan) const;
    void setHorizontalSpacing(int spacing);
    int horizontalSpacing() const;
    void setVerticalSpacing(int spacing);
    int verticalSpacing() const;
    virtual void setSpacing(int spacing);
    virtual int spacing() const;
    QLayoutItem *itemAtPosition(int row, int column) const;

protected:
    virtual void addItem(QLayoutItem * /Transfer/);
};
