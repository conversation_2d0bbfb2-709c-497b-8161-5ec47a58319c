// qstackedwidget.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QStackedWidget : public QFrame
{
%TypeHeaderCode
#include <qstackedwidget.h>
%End

public:
    explicit QStackedWidget(QWidget *parent /TransferThis/ = 0);
    virtual ~QStackedWidget();
    int addWidget(QWidget *w /Transfer/);
    int insertWidget(int index, QWidget *w /Transfer/);
    void removeWidget(QWidget *w);
    QWidget *currentWidget() const;
    int currentIndex() const;
    int indexOf(const QWidget *) const;
    QWidget *widget(int) const;
    int count() const /__len__/;

public slots:
    void setCurrentIndex(int index);
    void setCurrentWidget(QWidget *w);

signals:
    void currentChanged(int);
%If (Qt_6_9_0 -)
    void widgetAdded(int index);
%End
    void widgetRemoved(int index);

protected:
    virtual bool event(QEvent *e);
};
